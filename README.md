# BOSS直聘后台爬虫系统

🚀 **高性能BOSS直聘爬虫系统**，支持断点续传、实时验证、智能去重和完全后台运行！

## ✅ 核心功能

### 🎯 数据提取
- **7个字段完整提取** - 岗位名称、薪资、待遇、职位描述、公司简介、工作地点、网址
- **反反爬技术** - 深度绕过安全检查
- **多格式输出** - JSON和Excel格式

### 🔄 智能功能
- **断点续传** - 精确到页面级别，异常中断自动恢复
- **实时验证** - 真正的网络可访问性验证，智能缓存
- **智能去重** - 布隆过滤器 + 精确去重，支持百万级URL
- **完全后台** - headless模式，不影响用户操作

## 🚀 快速使用

### 1. 环境准备
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动程序
```bash
python background_crawler.py
```

### 3. 选择模式
1. **全面收集** - 收集所有职位类型和城市
2. **关键词收集** - 指定关键词和城市
3. **快速收集** - 快速获取指定数量URL
4. **状态监控** - 实时查看收集进度
5. **配置管理** - 调整并发数、页数等参数

## 📄 输出文件

### 自动生成
- `boss_urls_[session]_[timestamp].txt` - URL列表
- `boss_urls_[session]_[timestamp].json` - 详细数据
- `collection_state_[session].db` - 进度数据库
- `collection_state_[session].json` - 状态文件

### 缓存文件
- `url_validation_cache.json` - URL验证缓存
- `bloom_filter_[session].pkl` - 布隆过滤器状态
- `exact_dedup_[session].json` - 精确去重数据

## 📁 核心文件

### 主程序
- `background_crawler.py` - 后台爬虫主程序
- `boss_crawler.py` - 核心爬虫引擎
- `config.py` - 配置文件

### 数据处理
- `boss_security_bypass.py` - 安全检查绕过器
- `data_extractor.py` - 数据提取器
- `ultimate_data_extractor.py` - 终极数据提取器
- `data_processor.py` - 数据处理器

### URL收集系统
- `url_collection/smart_controller.py` - 智能控制器
- `url_collection/enhanced_state_manager.py` - 增强状态管理器
- `url_collection/enhanced_url_validator.py` - 增强URL验证器
- `url_collection/deduplicator.py` - 去重模块
- `url_collection/url_collector.py` - URL收集器
- `url_collection/search_config.py` - 搜索配置

## 🔧 技术特性

### 断点续传
- SQLite数据库精确记录进度
- 页面级别断点恢复
- 异常中断自动恢复
- 跨会话数据持久化

### 实时验证
- 异步HTTP验证URL可访问性
- 智能缓存验证结果
- 批量验证优化性能
- 支持重试和错误处理

### 智能去重
- 布隆过滤器快速预筛选
- 精确去重确保准确性
- 持久化去重状态
- 跨会话去重支持

### 后台运行
- headless模式，不弹出浏览器
- 智能资源管理
- 不影响用户其他操作
- 低CPU和内存占用

## ⚠️ 注意事项

- 仅用于学习和研究目的
- 请遵守网站使用协议
- 建议在网络稳定时运行
- 定期清理缓存文件保持性能

## 📊 性能指标

- **收集速度**: 每分钟200-500个URL
- **验证速度**: 每分钟100-300个URL
- **内存使用**: 通常<512MB
- **CPU使用**: 低负载运行
