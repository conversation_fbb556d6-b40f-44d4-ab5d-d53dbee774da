#!/usr/bin/env python3
"""
BOSS直聘后台爬虫演示程序
展示断点续传、实时验证、去重等核心功能
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from url_collection.smart_controller import SmartController
from url_collection.enhanced_url_validator import EnhancedURLValidator
from url_collection.deduplicator import Deduplicator

async def demo_url_validation():
    """演示URL验证功能"""
    print("\n🔍 演示URL验证功能")
    print("=" * 50)
    
    # 测试URL列表
    test_urls = [
        "https://www.zhipin.com/job_detail/valid_test.html",
        "https://www.zhipin.com/job_detail/another_test.html",
        "https://invalid-domain.com/test.html",
        "https://www.zhipin.com/job_detail/test123.html"
    ]
    
    async with EnhancedURLValidator() as validator:
        print(f"📋 测试URL数量: {len(test_urls)}")
        
        # 批量验证
        result = await validator.batch_validate_urls(test_urls)
        
        print(f"✅ 有效URL: {len(result['valid'])}")
        print(f"❌ 无效URL: {len(result['invalid'])}")
        print(f"📊 验证统计: {result['statistics']}")

def demo_deduplication():
    """演示去重功能"""
    print("\n🔄 演示去重功能")
    print("=" * 50)
    
    # 创建测试URL列表（包含重复）
    test_urls = [
        "https://www.zhipin.com/job_detail/test1.html",
        "https://www.zhipin.com/job_detail/test2.html",
        "https://www.zhipin.com/job_detail/test1.html",  # 重复
        "https://www.zhipin.com/job_detail/test3.html",
        "https://www.zhipin.com/job_detail/test2.html",  # 重复
        "https://www.zhipin.com/job_detail/test4.html",
        "https://www.zhipin.com/job_detail/test1.html",  # 重复
    ]
    
    dedup = Deduplicator("demo_session")
    
    print(f"📋 原始URL数量: {len(test_urls)}")
    
    # 批量去重
    unique_urls = dedup.batch_deduplicate(test_urls)
    
    print(f"✨ 去重后数量: {len(unique_urls)}")
    print(f"📊 重复率: {((len(test_urls) - len(unique_urls)) / len(test_urls) * 100):.1f}%")
    
    # 显示统计
    dedup.print_statistics()

async def demo_smart_controller():
    """演示智能控制器功能"""
    print("\n🎯 演示智能控制器功能")
    print("=" * 50)
    
    # 创建控制器
    session_name = f"demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    controller = SmartController(session_name)
    
    # 配置演示模式
    controller.set_config(
        max_concurrent_searches=1,
        max_pages_per_search=1,  # 只测试1页
        enable_real_time_validation=False,  # 演示时关闭验证
        enable_deduplication=True,
        headless_mode=True
    )
    
    print(f"📊 会话名称: {session_name}")
    
    # 设置回调函数
    def on_progress(status):
        print(f"📈 进度更新: {status.get('progress', 0):.1f}% - {status.get('current_activity', '')}")
    
    def on_url_found(urls, search_key):
        print(f"🔗 发现URL: {len(urls)} 个 - {search_key}")
    
    controller.set_callback("progress", on_progress)
    controller.set_callback("url_found", on_url_found)
    
    # 显示初始状态
    controller.print_status()
    
    print("💡 智能控制器演示完成")
    
    # 清理
    controller.cleanup()

async def demo_background_features():
    """演示后台功能特性"""
    print("\n🚀 演示后台功能特性")
    print("=" * 50)
    
    print("✅ 断点续传功能:")
    print("   - SQLite数据库精确记录进度")
    print("   - 页面级别断点恢复")
    print("   - 异常中断自动恢复")
    print("   - 跨会话数据持久化")
    
    print("\n✅ 实时验证功能:")
    print("   - 异步HTTP验证URL可访问性")
    print("   - 智能缓存验证结果")
    print("   - 批量验证优化性能")
    print("   - 支持重试和错误处理")
    
    print("\n✅ 智能去重功能:")
    print("   - 布隆过滤器快速预筛选")
    print("   - 精确去重确保准确性")
    print("   - 持久化去重状态")
    print("   - 跨会话去重支持")
    
    print("\n✅ 完全后台运行:")
    print("   - headless模式，不弹出浏览器")
    print("   - 智能资源管理")
    print("   - 不影响用户其他操作")
    print("   - 低CPU和内存占用")

def demo_file_structure():
    """演示文件结构"""
    print("\n📁 项目文件结构")
    print("=" * 50)
    
    structure = """
Position_Crawler/
├── background_crawler.py          # 后台爬虫主程序
├── start_background_crawler.py    # 快速启动脚本
├── demo_background_crawler.py     # 演示程序
├── BACKGROUND_CRAWLER_README.md   # 使用说明
├── config.py                      # 配置文件（已更新）
├── boss_crawler.py               # 核心爬虫（已更新）
├── requirements.txt              # 依赖包（已更新）
└── url_collection/               # URL收集模块
    ├── enhanced_state_manager.py    # 增强状态管理器
    ├── enhanced_url_validator.py    # 增强URL验证器
    ├── deduplicator.py             # 去重模块
    ├── smart_controller.py         # 智能控制器
    ├── url_collector.py            # URL收集器（已更新）
    ├── background_collector.py     # 后台收集器
    ├── search_config.py           # 搜索配置
    └── url_validator.py           # URL验证器
    """
    
    print(structure)

async def main():
    """主演示函数"""
    print("🎭 BOSS直聘后台爬虫系统演示")
    print("=" * 60)
    print("版本: v2.0")
    print("更新时间: 2025-07-02")
    print("=" * 60)
    
    try:
        # 演示各个功能模块
        demo_file_structure()
        await demo_background_features()
        
        print("\n" + "=" * 60)
        input("按回车键继续演示具体功能...")
        
        # 演示去重功能
        demo_deduplication()
        
        print("\n" + "=" * 60)
        input("按回车键继续演示URL验证...")
        
        # 演示URL验证功能
        await demo_url_validation()
        
        print("\n" + "=" * 60)
        input("按回车键继续演示智能控制器...")
        
        # 演示智能控制器
        await demo_smart_controller()
        
        print("\n🎉 演示完成！")
        print("\n📋 使用方式:")
        print("1. 快速启动: python start_background_crawler.py")
        print("2. 完整控制: python background_crawler.py")
        print("3. 查看文档: cat BACKGROUND_CRAWLER_README.md")
        
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"\n❌ 演示异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
