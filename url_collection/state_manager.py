"""
状态管理器
支持断点续传、进度保存和恢复
"""

import json
import time
import os
from typing import Dict, List, Set, Any
from datetime import datetime

class StateManager:
    """状态管理器 - 支持断点续传"""
    
    def __init__(self, session_name: str = None):
        if session_name is None:
            session_name = f"collection_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.session_name = session_name
        self.state_file = f"collection_state_{session_name}.json"
        self.urls_file = f"collected_urls_{session_name}.txt"
        
        # 状态数据
        self.state = {
            "session_name": session_name,
            "start_time": time.time(),
            "last_update": time.time(),
            "total_searches": 0,
            "completed_searches": 0,
            "collected_urls": set(),
            "validated_urls": set(),
            "invalid_urls": set(),
            "search_progress": {},
            "failed_searches": [],
            "statistics": {
                "total_pages_processed": 0,
                "total_urls_found": 0,
                "total_urls_validated": 0,
                "validation_success_rate": 0.0
            }
        }
        
        # 尝试加载现有状态
        self.load_state()
    
    def save_state(self):
        """保存状态到文件"""
        try:
            # 转换set为list以便JSON序列化
            state_copy = self.state.copy()
            state_copy["collected_urls"] = list(self.state["collected_urls"])
            state_copy["validated_urls"] = list(self.state["validated_urls"])
            state_copy["invalid_urls"] = list(self.state["invalid_urls"])
            state_copy["last_update"] = time.time()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_copy, f, ensure_ascii=False, indent=2)
            
            # 同时保存URL列表
            self.save_urls()
            
        except Exception as e:
            print(f"⚠️  保存状态失败: {e}")
    
    def load_state(self):
        """从文件加载状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    loaded_state = json.load(f)
                
                # 转换list为set
                loaded_state["collected_urls"] = set(loaded_state.get("collected_urls", []))
                loaded_state["validated_urls"] = set(loaded_state.get("validated_urls", []))
                loaded_state["invalid_urls"] = set(loaded_state.get("invalid_urls", []))
                
                self.state.update(loaded_state)
                print(f"✅ 加载状态成功: {len(self.state['collected_urls'])} 个已收集URL")
                
        except Exception as e:
            print(f"⚠️  加载状态失败: {e}")
    
    def save_urls(self):
        """保存URL列表到文件"""
        try:
            with open(self.urls_file, 'w', encoding='utf-8') as f:
                for url in sorted(self.state["validated_urls"]):
                    f.write(f"{url}\n")
        except Exception as e:
            print(f"⚠️  保存URL列表失败: {e}")
    
    def add_urls(self, urls: List[str], source: str = "unknown"):
        """添加URL（自动去重）"""
        new_urls = []
        for url in urls:
            if url not in self.state["collected_urls"]:
                self.state["collected_urls"].add(url)
                new_urls.append(url)
        
        if new_urls:
            self.state["statistics"]["total_urls_found"] += len(new_urls)
            print(f"📊 新增 {len(new_urls)} 个URL (来源: {source})")
        
        return new_urls
    
    def mark_url_validated(self, url: str, is_valid: bool):
        """标记URL验证结果"""
        if is_valid:
            self.state["validated_urls"].add(url)
        else:
            self.state["invalid_urls"].add(url)
        
        self.state["statistics"]["total_urls_validated"] += 1
        
        # 更新验证成功率
        total_validated = len(self.state["validated_urls"]) + len(self.state["invalid_urls"])
        if total_validated > 0:
            self.state["statistics"]["validation_success_rate"] = \
                len(self.state["validated_urls"]) / total_validated * 100
    
    def update_search_progress(self, search_key: str, page: int, status: str):
        """更新搜索进度"""
        if search_key not in self.state["search_progress"]:
            self.state["search_progress"][search_key] = {
                "completed_pages": 0,
                "last_page": 0,
                "status": "pending"
            }
        
        self.state["search_progress"][search_key]["last_page"] = page
        self.state["search_progress"][search_key]["status"] = status
        
        if status == "completed":
            self.state["search_progress"][search_key]["completed_pages"] = page
            self.state["completed_searches"] += 1
    
    def get_search_resume_point(self, search_key: str) -> int:
        """获取搜索的恢复点"""
        if search_key in self.state["search_progress"]:
            progress = self.state["search_progress"][search_key]
            if progress["status"] == "completed":
                return -1  # 已完成，跳过
            return progress["last_page"] + 1
        return 1  # 从第一页开始
    
    def is_search_completed(self, search_key: str) -> bool:
        """检查搜索是否已完成"""
        if search_key in self.state["search_progress"]:
            return self.state["search_progress"][search_key]["status"] == "completed"
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        runtime = time.time() - self.state["start_time"]
        
        stats = self.state["statistics"].copy()
        stats.update({
            "session_name": self.session_name,
            "runtime_seconds": runtime,
            "runtime_formatted": self.format_duration(runtime),
            "total_collected": len(self.state["collected_urls"]),
            "total_validated": len(self.state["validated_urls"]),
            "total_invalid": len(self.state["invalid_urls"]),
            "pending_validation": len(self.state["collected_urls"]) - 
                                len(self.state["validated_urls"]) - 
                                len(self.state["invalid_urls"]),
            "search_progress": f"{self.state['completed_searches']}/{self.state['total_searches']}",
            "urls_per_minute": len(self.state["validated_urls"]) / (runtime / 60) if runtime > 0 else 0
        })
        
        return stats
    
    def format_duration(self, seconds: float) -> str:
        """格式化时间"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            return f"{seconds/60:.1f}分钟"
        else:
            return f"{seconds/3600:.1f}小时"
    
    def print_progress(self):
        """打印进度信息"""
        stats = self.get_statistics()
        
        print(f"\n📊 收集进度报告")
        print(f"=" * 50)
        print(f"🕐 运行时间: {stats['runtime_formatted']}")
        print(f"🔍 搜索进度: {stats['search_progress']}")
        print(f"📋 已收集: {stats['total_collected']} 个URL")
        print(f"✅ 已验证: {stats['total_validated']} 个有效URL")
        print(f"❌ 无效URL: {stats['total_invalid']} 个")
        print(f"⏳ 待验证: {stats['pending_validation']} 个")
        print(f"📈 验证成功率: {stats['validation_success_rate']:.1f}%")
        print(f"⚡ 收集速度: {stats['urls_per_minute']:.1f} URL/分钟")
        print(f"=" * 50)
    
    def cleanup_old_sessions(self, keep_days: int = 7):
        """清理旧的会话文件"""
        try:
            current_time = time.time()
            for filename in os.listdir('.'):
                if filename.startswith('collection_state_') and filename.endswith('.json'):
                    file_time = os.path.getmtime(filename)
                    if current_time - file_time > keep_days * 24 * 3600:
                        os.remove(filename)
                        # 同时删除对应的URL文件
                        url_file = filename.replace('collection_state_', 'collected_urls_').replace('.json', '.txt')
                        if os.path.exists(url_file):
                            os.remove(url_file)
                        print(f"🗑️  清理旧会话: {filename}")
        except Exception as e:
            print(f"⚠️  清理旧会话失败: {e}")
    
    def export_final_results(self, output_file: str = None):
        """导出最终结果"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"boss_job_urls_final_{timestamp}.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for url in sorted(self.state["validated_urls"]):
                    f.write(f"{url}\n")
            
            print(f"✅ 最终结果已导出到: {output_file}")
            print(f"📊 总计 {len(self.state['validated_urls'])} 个有效URL")
            
            return output_file
            
        except Exception as e:
            print(f"❌ 导出结果失败: {e}")
            return None
