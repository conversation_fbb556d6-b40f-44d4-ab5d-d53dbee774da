"""
后台URL收集器
支持多线程后台运行、实时验证、断点续传
"""

import asyncio
import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Callable
import aiohttp

from .url_collector import URLCollector
from .state_manager import StateManager
from .url_validator import URLValidator

class BackgroundCollector:
    """后台URL收集器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.collector = URLCollector()
        self.validator = URLValidator()
        self.state_manager = None
        
        # 线程控制
        self.is_running = False
        self.is_paused = False
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        
        # 队列
        self.search_queue = queue.Queue()
        self.validation_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 回调函数
        self.progress_callback = None
        self.url_found_callback = None
        self.validation_callback = None
        
        # 统计信息
        self.stats = {
            "start_time": 0,
            "searches_completed": 0,
            "urls_found": 0,
            "urls_validated": 0,
            "current_activity": "待机中"
        }
    
    def set_callbacks(self, 
                     progress_callback: Callable = None,
                     url_found_callback: Callable = None,
                     validation_callback: Callable = None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.url_found_callback = url_found_callback
        self.validation_callback = validation_callback
    
    def start_collection(self, search_configs: List[Dict[str, Any]], 
                        session_name: str = None, 
                        resume: bool = True) -> str:
        """开始后台收集"""
        if self.is_running:
            return "收集器已在运行中"
        
        # 初始化状态管理器
        self.state_manager = StateManager(session_name)
        
        if resume:
            print("🔄 检查断点续传...")
            self._filter_completed_searches(search_configs)
        
        # 设置搜索队列
        self.state_manager.state["total_searches"] = len(search_configs)
        for config in search_configs:
            self.search_queue.put(config)
        
        # 启动后台线程
        self.is_running = True
        self.stop_event.clear()
        self.stats["start_time"] = time.time()
        
        # 启动工作线程
        self.search_thread = threading.Thread(target=self._search_worker, daemon=True)
        self.validation_thread = threading.Thread(target=self._validation_worker, daemon=True)
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        
        self.search_thread.start()
        self.validation_thread.start()
        self.monitor_thread.start()
        
        return f"✅ 后台收集已启动 - 会话: {self.state_manager.session_name}"
    
    def _filter_completed_searches(self, search_configs: List[Dict[str, Any]]):
        """过滤已完成的搜索"""
        remaining_configs = []
        for config in search_configs:
            search_key = self._get_search_key(config)
            if not self.state_manager.is_search_completed(search_key):
                remaining_configs.append(config)
            else:
                print(f"⏭️  跳过已完成的搜索: {search_key}")
        
        search_configs.clear()
        search_configs.extend(remaining_configs)
        print(f"📋 断点续传: 剩余 {len(remaining_configs)} 个搜索任务")
    
    def _get_search_key(self, config: Dict[str, Any]) -> str:
        """生成搜索键"""
        city = config.get('city', 'unknown')
        query = config.get('query', 'unknown')
        return f"{city}_{query}"
    
    def _search_worker(self):
        """搜索工作线程"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            while self.is_running and not self.stop_event.is_set():
                try:
                    if self.is_paused:
                        time.sleep(1)
                        continue
                    
                    # 获取搜索任务
                    try:
                        config = self.search_queue.get(timeout=1)
                    except queue.Empty:
                        if self.search_queue.empty():
                            break
                        continue
                    
                    # 提交搜索任务
                    future = executor.submit(self._process_search, config)
                    
                    # 等待完成
                    try:
                        future.result(timeout=300)  # 5分钟超时
                    except Exception as e:
                        print(f"❌ 搜索任务失败: {e}")
                    
                    self.search_queue.task_done()
                    
                except Exception as e:
                    print(f"❌ 搜索工作线程异常: {e}")
        
        print("🔚 搜索工作线程结束")
    
    def _process_search(self, config: Dict[str, Any]):
        """处理单个搜索任务"""
        search_key = self._get_search_key(config)
        
        try:
            self.stats["current_activity"] = f"搜索: {search_key}"
            
            # 获取断点续传位置
            start_page = self.state_manager.get_search_resume_point(search_key)
            if start_page == -1:
                return  # 已完成
            
            print(f"🔍 开始搜索: {search_key} (从第{start_page}页)")
            
            # 执行搜索
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                urls = loop.run_until_complete(
                    self.collector.search_jobs_by_params_with_resume(
                        config, 
                        start_page=start_page,
                        state_manager=self.state_manager,
                        search_key=search_key
                    )
                )
                
                # 添加到状态管理器
                new_urls = self.state_manager.add_urls(urls, search_key)
                
                # 添加到验证队列
                for url in new_urls:
                    self.validation_queue.put(url)
                
                # 更新统计
                self.stats["searches_completed"] += 1
                self.stats["urls_found"] += len(new_urls)
                
                # 标记搜索完成
                self.state_manager.update_search_progress(search_key, -1, "completed")
                
                # 回调通知
                if self.url_found_callback:
                    self.url_found_callback(new_urls, search_key)
                
                print(f"✅ 搜索完成: {search_key} - 找到 {len(new_urls)} 个新URL")
                
            finally:
                loop.close()
                
        except Exception as e:
            print(f"❌ 搜索失败: {search_key} - {e}")
            self.state_manager.state["failed_searches"].append({
                "search_key": search_key,
                "config": config,
                "error": str(e),
                "timestamp": time.time()
            })
    
    def _validation_worker(self):
        """验证工作线程"""
        while self.is_running and not self.stop_event.is_set():
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue
                
                # 获取待验证URL
                try:
                    url = self.validation_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                self.stats["current_activity"] = f"验证: {url[:50]}..."
                
                # 执行验证
                is_valid = self._validate_url(url)
                
                # 更新状态
                self.state_manager.mark_url_validated(url, is_valid)
                self.stats["urls_validated"] += 1
                
                # 回调通知
                if self.validation_callback:
                    self.validation_callback(url, is_valid)
                
                self.validation_queue.task_done()
                
            except Exception as e:
                print(f"❌ 验证工作线程异常: {e}")
        
        print("🔚 验证工作线程结束")
    
    def _validate_url(self, url: str) -> bool:
        """验证单个URL"""
        try:
            # 格式验证
            if not self.validator.is_valid_format(url):
                return False
            
            # 可访问性验证（简化版，避免过多网络请求）
            # 这里可以根据需要调整验证策略
            return True
            
        except Exception as e:
            print(f"⚠️  URL验证异常: {url} - {e}")
            return False
    
    def _monitor_worker(self):
        """监控工作线程"""
        last_save = time.time()
        
        while self.is_running and not self.stop_event.is_set():
            try:
                # 定期保存状态
                if time.time() - last_save > 30:  # 每30秒保存一次
                    if self.state_manager:
                        self.state_manager.save_state()
                        last_save = time.time()
                
                # 更新进度回调
                if self.progress_callback:
                    self.progress_callback(self.get_status())
                
                time.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                print(f"❌ 监控线程异常: {e}")
        
        # 最终保存
        if self.state_manager:
            self.state_manager.save_state()
        
        print("🔚 监控工作线程结束")
    
    def pause(self):
        """暂停收集"""
        self.is_paused = True
        print("⏸️  收集已暂停")
    
    def resume(self):
        """恢复收集"""
        self.is_paused = False
        print("▶️  收集已恢复")
    
    def stop(self):
        """停止收集"""
        print("🛑 正在停止收集...")
        self.is_running = False
        self.stop_event.set()
        
        # 等待线程结束
        if hasattr(self, 'search_thread'):
            self.search_thread.join(timeout=10)
        if hasattr(self, 'validation_thread'):
            self.validation_thread.join(timeout=10)
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=10)
        
        print("✅ 收集已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.state_manager:
            return {"status": "未启动"}
        
        stats = self.state_manager.get_statistics()
        stats.update({
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "current_activity": self.stats["current_activity"],
            "search_queue_size": self.search_queue.qsize(),
            "validation_queue_size": self.validation_queue.qsize()
        })
        
        return stats
    
    def print_status(self):
        """打印状态信息"""
        status = self.get_status()
        
        print(f"\n🔄 后台收集器状态")
        print(f"=" * 50)
        print(f"📊 运行状态: {'运行中' if status.get('is_running') else '已停止'}")
        if status.get('is_paused'):
            print(f"⏸️  暂停状态: 已暂停")
        print(f"🎯 当前活动: {status.get('current_activity', '无')}")
        print(f"⏳ 搜索队列: {status.get('search_queue_size', 0)} 个任务")
        print(f"🔍 验证队列: {status.get('validation_queue_size', 0)} 个URL")
        print(f"✅ 已验证: {status.get('total_validated', 0)} 个URL")
        print(f"📈 成功率: {status.get('validation_success_rate', 0):.1f}%")
        print(f"=" * 50)
