# BOSS直聘后台爬虫系统 - 使用指南

## 🎯 系统概述

本系统是BOSS直聘爬虫的全面升级版本，实现了您要求的所有核心功能：

- ✅ **断点续传** - 精确到页面级别，异常中断自动恢复
- ✅ **实时有效校验** - 真正的网络验证，智能缓存
- ✅ **智能去重** - 布隆过滤器 + 精确去重，支持百万级URL
- ✅ **完全后台运行** - headless模式，不影响用户操作

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
source venv/bin/activate

# 确认依赖已安装
pip install -r requirements.txt
```

### 2. 三种启动方式

#### 方式一：快速启动（推荐）
```bash
python start_background_crawler.py
```
- 自动启动全面收集模式
- 完全后台运行，不弹出浏览器
- 自动保存结果到文件
- 支持Ctrl+C优雅停止

#### 方式二：完整控制界面
```bash
python background_crawler.py
```
- 提供交互式菜单
- 支持多种收集模式
- 实时状态监控
- 灵活的配置调整

#### 方式三：演示模式
```bash
python demo_background_crawler.py
```
- 展示所有功能特性
- 无需实际爬取
- 适合了解系统能力

## 📊 功能特性详解

### 断点续传功能
- **SQLite数据库**: 精确记录每个搜索任务的页面进度
- **自动恢复**: 程序异常中断后，重启时自动从断点继续
- **跨会话支持**: 不同时间启动的会话可以共享进度
- **数据完整性**: 事务性操作确保数据一致性

### 实时验证功能
- **网络验证**: 真正的HTTP请求验证URL可访问性
- **智能缓存**: 24小时缓存验证结果，避免重复验证
- **批量处理**: 异步批量验证，提升效率
- **错误处理**: 自动重试和详细错误记录

### 智能去重功能
- **布隆过滤器**: 快速预筛选，支持百万级URL
- **精确去重**: 确保100%准确性
- **持久化**: 去重状态保存到文件，跨会话有效
- **统计分析**: 详细的去重效率统计

### 后台运行功能
- **Headless模式**: 浏览器完全在后台运行
- **资源优化**: 禁用图片、插件等，降低资源占用
- **智能调度**: 自适应并发控制，不影响系统性能
- **用户友好**: 不弹出任何窗口，静默运行

## 🔧 配置说明

### 性能配置
```python
# 在程序中可调整的参数
max_concurrent_searches = 3    # 最大并发搜索数
max_pages_per_search = 20      # 每次搜索最大页数
enable_real_time_validation = True  # 启用实时验证
enable_deduplication = True    # 启用去重
```

### 后台模式配置
```python
# config.py中的BACKGROUND_MODE配置
headless = True               # 无头模式
disable_images = True         # 禁用图片
max_memory_mb = 512          # 最大内存使用
```

## 📁 输出文件说明

### 自动生成的文件
1. **URL列表文件**: `boss_urls_[session]_[timestamp].txt`
   - 纯文本格式，每行一个URL
   - 只包含验证有效的URL

2. **详细数据文件**: `boss_urls_[session]_[timestamp].json`
   - JSON格式，包含完整统计信息
   - 会话信息、收集时间、验证结果等

3. **进度数据库**: `collection_state_[session].db`
   - SQLite数据库，记录详细进度
   - 支持断点续传的核心文件

4. **状态文件**: `collection_state_[session].json`
   - JSON格式的状态快照
   - 便于查看收集进度

### 缓存文件
- `url_validation_cache.json` - URL验证缓存
- `bloom_filter_[session].pkl` - 布隆过滤器状态
- `exact_dedup_[session].json` - 精确去重数据

## 📈 使用建议

### 最佳实践
1. **首次使用**: 建议先运行演示程序了解功能
2. **日常使用**: 使用快速启动模式，简单高效
3. **高级控制**: 需要精细控制时使用完整界面
4. **长期运行**: 定期清理缓存文件，保持性能

### 性能优化
1. **网络环境**: 在网络稳定时运行效果最佳
2. **并发控制**: 根据网络状况调整并发数
3. **资源监控**: 注意内存和CPU使用情况
4. **定期维护**: 清理过期缓存和临时文件

### 故障排除
1. **启动失败**: 检查依赖包和Python版本
2. **收集缓慢**: 调整并发数和网络设置
3. **内存不足**: 降低并发数或启用单进程模式
4. **验证失败**: 检查网络连接和防火墙设置

## 🔒 注意事项

### 合规使用
- 仅用于学习和研究目的
- 遵守网站的robots.txt规则
- 不要过度频繁请求

### 技术限制
- 需要稳定的网络连接
- Python 3.8+ 环境
- 足够的磁盘空间存储数据

### 安全考虑
- 所有数据本地存储
- 不上传任何个人信息
- 建议在安全的网络环境中使用

## 📞 技术支持

### 常见问题
1. **Q: 如何查看收集进度？**
   A: 使用完整控制界面的"查看状态"功能

2. **Q: 如何恢复中断的收集？**
   A: 重新启动程序，系统会自动检测并恢复

3. **Q: 如何清理历史数据？**
   A: 使用完整控制界面的"清理数据"功能

4. **Q: 如何调整收集参数？**
   A: 使用完整控制界面的"配置设置"功能

### 文件说明
- `BACKGROUND_CRAWLER_README.md` - 详细技术文档
- `demo_background_crawler.py` - 功能演示程序
- `使用指南.md` - 本文件，用户指南

---

**版本**: v2.0  
**更新时间**: 2025-07-02  
**作者**: Augment Agent  
**兼容性**: Python 3.8+
