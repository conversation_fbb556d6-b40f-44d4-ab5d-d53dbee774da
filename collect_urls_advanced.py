#!/usr/bin/env python3
"""
BOSS直聘岗位URL收集器 - 高级版
支持后台运行、断点续传、实时验证、交互式控制

使用方法:
    python collect_urls_advanced.py                    # 交互式模式
    python collect_urls_advanced.py --background       # 后台模式
    python collect_urls_advanced.py --resume           # 恢复上次会话
    python collect_urls_advanced.py --status           # 查看状态
    python collect_urls_advanced.py --help             # 显示帮助
"""

import asyncio
import sys
import os
import argparse
import time
import threading
from datetime import datetime

from url_collection import SearchConfig
from url_collection.background_collector import BackgroundCollector
from url_collection.state_manager import StateManager

class AdvancedURLCollector:
    """高级URL收集器"""
    
    def __init__(self):
        self.background_collector = BackgroundCollector(max_workers=2)
        self.search_config = SearchConfig()
        self.is_interactive = True
        
        # 设置回调函数
        self.background_collector.set_callbacks(
            progress_callback=self.on_progress_update,
            url_found_callback=self.on_urls_found,
            validation_callback=self.on_url_validated
        )
    
    def on_progress_update(self, status):
        """进度更新回调"""
        if self.is_interactive:
            # 清除当前行并打印新状态
            print(f"\r🔄 {status.get('current_activity', '处理中')}... "
                  f"已验证: {status.get('total_validated', 0)} | "
                  f"成功率: {status.get('validation_success_rate', 0):.1f}%", end='', flush=True)
    
    def on_urls_found(self, urls, source):
        """发现URL回调"""
        if len(urls) > 0:
            print(f"\n✨ 发现 {len(urls)} 个新URL (来源: {source})")
    
    def on_url_validated(self, url, is_valid):
        """URL验证回调"""
        # 这里可以添加实时验证通知，但为了避免输出过多，暂时省略
        pass
    
    def print_banner(self):
        """打印程序横幅"""
        print("🚀 BOSS直聘岗位URL收集器 - 高级版")
        print("=" * 60)
        print("✅ 后台运行 + 断点续传 + 实时验证")
        print("✅ 多线程并发 + 智能去重")
        print("✅ 交互式控制 + 状态监控")
        print("=" * 60)
    
    def start_background_collection(self, session_name=None, resume=True):
        """启动后台收集"""
        # 获取搜索配置
        search_configs = self.search_config.get_comprehensive_search_configs()
        
        print(f"📊 准备启动后台收集")
        print(f"🎯 搜索配置数: {len(search_configs)}")
        
        # 启动后台收集
        result = self.background_collector.start_collection(
            search_configs, 
            session_name=session_name,
            resume=resume
        )
        
        print(result)
        return True
    
    def interactive_control(self):
        """交互式控制界面"""
        print("\n🎮 交互式控制界面")
        print("=" * 40)
        print("命令列表:")
        print("  status  - 查看状态")
        print("  pause   - 暂停收集")
        print("  resume  - 恢复收集")
        print("  stop    - 停止收集")
        print("  export  - 导出结果")
        print("  help    - 显示帮助")
        print("  quit    - 退出程序")
        print("=" * 40)
        
        while True:
            try:
                command = input("\n请输入命令: ").strip().lower()
                
                if command == "status":
                    self.show_status()
                elif command == "pause":
                    self.background_collector.pause()
                elif command == "resume":
                    self.background_collector.resume()
                elif command == "stop":
                    self.background_collector.stop()
                    break
                elif command == "export":
                    self.export_results()
                elif command == "help":
                    self.show_help()
                elif command in ["quit", "exit", "q"]:
                    self.background_collector.stop()
                    break
                else:
                    print(f"❌ 未知命令: {command}")
                    
            except KeyboardInterrupt:
                print("\n\n🛑 收到中断信号，正在停止...")
                self.background_collector.stop()
                break
            except Exception as e:
                print(f"❌ 命令执行异常: {e}")
    
    def show_status(self):
        """显示状态"""
        self.background_collector.print_status()
        if self.background_collector.state_manager:
            self.background_collector.state_manager.print_progress()
    
    def export_results(self):
        """导出结果"""
        if self.background_collector.state_manager:
            output_file = self.background_collector.state_manager.export_final_results()
            if output_file:
                print(f"✅ 结果已导出到: {output_file}")
        else:
            print("❌ 没有可导出的数据")
    
    def show_help(self):
        """显示帮助"""
        print("\n📋 命令说明:")
        print("  status  - 显示收集进度和统计信息")
        print("  pause   - 暂停后台收集（可恢复）")
        print("  resume  - 恢复暂停的收集")
        print("  stop    - 完全停止收集并保存状态")
        print("  export  - 导出当前收集到的有效URL")
        print("  help    - 显示此帮助信息")
        print("  quit    - 停止收集并退出程序")
    
    def background_mode(self, session_name=None):
        """后台模式"""
        self.is_interactive = False
        print("🔄 启动后台模式...")
        
        # 启动收集
        if self.start_background_collection(session_name=session_name):
            print("✅ 后台收集已启动")
            print("💡 使用 'python collect_urls_advanced.py --status' 查看进度")
            print("💡 使用 Ctrl+C 停止收集")
            
            try:
                # 等待完成或中断
                while self.background_collector.is_running:
                    time.sleep(5)
                    status = self.background_collector.get_status()
                    if status.get('search_queue_size', 0) == 0 and status.get('validation_queue_size', 0) == 0:
                        print("\n🎉 收集完成！")
                        break
            except KeyboardInterrupt:
                print("\n🛑 收到中断信号，正在停止...")
                self.background_collector.stop()
    
    def resume_session(self, session_name=None):
        """恢复会话"""
        print("🔄 恢复上次会话...")
        
        # 查找最近的会话
        if session_name is None:
            session_files = [f for f in os.listdir('.') if f.startswith('collection_state_') and f.endswith('.json')]
            if not session_files:
                print("❌ 没有找到可恢复的会话")
                return False
            
            # 选择最新的会话
            session_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            latest_session = session_files[0]
            session_name = latest_session.replace('collection_state_', '').replace('.json', '')
            print(f"📂 找到最近会话: {session_name}")
        
        return self.start_background_collection(session_name=session_name, resume=True)
    
    def show_session_status(self):
        """显示会话状态"""
        session_files = [f for f in os.listdir('.') if f.startswith('collection_state_') and f.endswith('.json')]
        
        if not session_files:
            print("❌ 没有找到活动会话")
            return
        
        print("📊 活动会话状态:")
        print("=" * 50)
        
        for session_file in sorted(session_files, key=lambda x: os.path.getmtime(x), reverse=True):
            session_name = session_file.replace('collection_state_', '').replace('.json', '')
            
            try:
                state_manager = StateManager(session_name)
                stats = state_manager.get_statistics()
                
                print(f"📂 会话: {session_name}")
                print(f"   ⏰ 运行时间: {stats['runtime_formatted']}")
                print(f"   📋 已收集: {stats['total_collected']} 个URL")
                print(f"   ✅ 已验证: {stats['total_validated']} 个有效URL")
                print(f"   📈 成功率: {stats['validation_success_rate']:.1f}%")
                print(f"   🔍 搜索进度: {stats['search_progress']}")
                print()
                
            except Exception as e:
                print(f"   ❌ 读取会话失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BOSS直聘岗位URL收集器 - 高级版")
    parser.add_argument('--background', action='store_true', help='后台模式')
    parser.add_argument('--resume', type=str, nargs='?', const='auto', help='恢复会话')
    parser.add_argument('--status', action='store_true', help='查看状态')
    parser.add_argument('--session', type=str, help='指定会话名称')
    
    args = parser.parse_args()
    
    collector = AdvancedURLCollector()
    collector.print_banner()
    
    if args.status:
        collector.show_session_status()
        return
    
    if args.resume:
        session_name = args.resume if args.resume != 'auto' else None
        if collector.resume_session(session_name):
            collector.interactive_control()
        return
    
    if args.background:
        collector.background_mode(session_name=args.session)
        return
    
    # 默认交互式模式
    print("\n🎯 选择运行模式:")
    print("1. 启动新的收集任务")
    print("2. 恢复上次会话")
    print("3. 查看会话状态")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            if collector.start_background_collection(session_name=args.session):
                collector.interactive_control()
        elif choice == "2":
            if collector.resume_session():
                collector.interactive_control()
        elif choice == "3":
            collector.show_session_status()
        elif choice == "4":
            print("👋 再见！")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
