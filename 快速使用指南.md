# BOSS直聘后台爬虫 - 快速使用指南

## 🚀 一键启动

```bash
# 1. 激活环境
source venv/bin/activate

# 2. 启动程序
python background_crawler.py
```

## 📋 功能菜单

启动后会看到交互式菜单：

```
📋 操作菜单:
1. 开始全面收集    # 收集所有职位和城市
2. 关键词收集      # 指定关键词和城市
3. 快速收集        # 快速获取指定数量URL
4. 查看状态        # 实时监控进度
5. 暂停/恢复       # 控制收集过程
6. 停止收集        # 安全停止
7. 查看统计        # 详细统计信息
8. 配置设置        # 调整参数
9. 清理数据        # 清理历史数据
0. 退出程序
```

## ⚡ 核心特性

- ✅ **断点续传** - 异常中断自动恢复
- ✅ **实时验证** - 网络验证URL有效性
- ✅ **智能去重** - 布隆过滤器+精确去重
- ✅ **完全后台** - headless模式，不弹窗

## 📄 输出文件

程序会自动生成：
- `boss_urls_[session]_[timestamp].txt` - URL列表
- `boss_urls_[session]_[timestamp].json` - 详细数据
- `collection_state_[session].db` - 进度数据库

## 🔧 推荐配置

- **并发数**: 2-3（避免触发反爬）
- **页数**: 15-20（平衡速度和稳定性）
- **验证**: 开启（确保URL有效性）
- **去重**: 开启（避免重复数据）

## ⚠️ 注意事项

1. 首次运行建议选择"快速收集"测试
2. 网络不稳定时降低并发数
3. 定期清理缓存文件
4. 仅用于学习研究目的
