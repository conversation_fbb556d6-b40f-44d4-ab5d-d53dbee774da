# BOSS直聘后台爬虫系统 v2.0

## 🚀 核心特性

- ✅ **断点续传** - 精确到页面级别的进度恢复
- ✅ **实时验证** - 真正的网络可访问性验证
- ✅ **智能去重** - 布隆过滤器 + 精确去重
- ✅ **完全后台** - headless模式，不影响用户操作
- ✅ **多格式输出** - JSON + TXT格式
- ✅ **性能优化** - 智能并发控制和资源管理

## 📦 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 快速启动（推荐）
```bash
python start_background_crawler.py
```

### 3. 完整控制界面
```bash
python background_crawler.py
```

## 🎯 使用方式

### 快速启动模式
- 自动启动全面收集
- 后台运行，不弹出浏览器窗口
- 自动保存结果到文件
- 支持 Ctrl+C 优雅停止

### 完整控制模式
提供交互式菜单：
1. **开始全面收集** - 收集所有职位类型和城市
2. **关键词收集** - 指定关键词和城市
3. **快速收集** - 快速获取指定数量URL
4. **查看状态** - 实时监控收集进度
5. **暂停/恢复** - 灵活控制收集过程
6. **停止收集** - 安全停止并保存结果
7. **查看统计** - 详细的收集和验证统计
8. **配置设置** - 调整并发数、页数等参数
9. **清理数据** - 清理历史数据和缓存

## 🔧 核心功能

### 断点续传
- 使用SQLite数据库精确记录进度
- 支持页面级别的断点恢复
- 异常中断后自动恢复
- 跨会话数据持久化

### 实时验证
- 异步HTTP验证URL可访问性
- 智能缓存验证结果
- 批量验证优化性能
- 支持重试和错误处理

### 智能去重
- 布隆过滤器快速预筛选
- 精确去重确保准确性
- 持久化去重状态
- 跨会话去重支持

### 后台运行
- 完全headless模式
- 禁用图片加载节省带宽
- 智能资源管理
- 不影响用户其他操作

## 📊 输出文件

### 自动生成文件
- `boss_urls_[session]_[timestamp].txt` - URL列表
- `boss_urls_[session]_[timestamp].json` - 详细数据
- `collection_state_[session].db` - 进度数据库
- `collection_state_[session].json` - 状态文件

### 统计信息
- 收集进度和完成率
- URL验证成功率
- 去重效率统计
- 运行时间和性能指标

## ⚙️ 配置选项

### 性能配置
- `max_concurrent_searches`: 最大并发搜索数 (默认: 3)
- `max_pages_per_search`: 每次搜索最大页数 (默认: 20)
- `enable_real_time_validation`: 启用实时验证 (默认: true)
- `enable_deduplication`: 启用去重 (默认: true)

### 后台模式配置
- `headless`: 无头模式 (默认: true)
- `disable_images`: 禁用图片 (默认: true)
- `max_memory_mb`: 最大内存使用 (默认: 512MB)

## 🛠️ 故障排除

### 常见问题

1. **启动失败**
   - 检查Python版本 (需要3.8+)
   - 确认依赖包已安装
   - 检查网络连接

2. **收集速度慢**
   - 调整并发数配置
   - 检查网络状况
   - 减少验证频率

3. **内存使用过高**
   - 降低并发数
   - 启用单进程模式
   - 减少缓存大小

4. **验证失败率高**
   - 检查网络稳定性
   - 调整验证超时时间
   - 更新User-Agent

### 日志和调试
- 程序运行时会输出详细日志
- 错误信息会显示具体原因
- 可通过状态文件查看详细进度

## 📈 性能指标

### 典型性能
- **收集速度**: 每分钟200-500个URL
- **验证速度**: 每分钟100-300个URL
- **内存使用**: 通常<512MB
- **CPU使用**: 低负载运行

### 优化建议
- 在网络良好时运行
- 避免高峰时段
- 定期清理缓存文件
- 监控系统资源使用

## 🔒 注意事项

1. **合规使用**: 仅用于学习和研究目的
2. **频率控制**: 内置延时机制，避免过度请求
3. **资源管理**: 自动控制并发数和内存使用
4. **数据安全**: 本地存储，不上传任何数据

## 📞 技术支持

如遇问题，请检查：
1. 网络连接状态
2. 依赖包版本
3. 系统资源使用情况
4. 错误日志信息

---

**版本**: v2.0  
**更新时间**: 2025-07-02  
**兼容性**: Python 3.8+
