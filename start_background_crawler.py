#!/usr/bin/env python3
"""
BOSS直聘后台爬虫启动脚本
快速启动后台收集任务
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from url_collection.smart_controller import SmartController

async def quick_start():
    """快速启动后台收集"""
    print("🚀 BOSS直聘后台爬虫 - 快速启动")
    print("=" * 50)
    
    # 创建控制器
    session_name = f"auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    controller = SmartController(session_name)
    
    # 配置后台模式
    controller.set_config(
        max_concurrent_searches=2,  # 降低并发以减少资源占用
        max_pages_per_search=15,
        enable_real_time_validation=True,
        enable_deduplication=True,
        headless_mode=True,
        save_interval=60,  # 每分钟保存一次
        progress_report_interval=30  # 每30秒报告一次进度
    )
    
    # 设置简单的进度回调
    def on_progress(status):
        if status.get("progress", 0) > 0:
            print(f"📊 进度: {status['progress']:.1f}% - {status.get('current_activity', '')}")
    
    def on_url_found(urls, search_key):
        print(f"🔗 发现 {len(urls)} 个URL - {search_key}")
    
    controller.set_callback("progress", on_progress)
    controller.set_callback("url_found", on_url_found)
    
    try:
        # 启动收集
        print("🎯 启动全面收集模式...")
        result = await controller.start_collection("comprehensive")
        print(result)
        
        if "✅" in result:
            print("\n📊 收集已在后台启动")
            print("💡 程序将在后台运行，您可以:")
            print("   - 按 Ctrl+C 停止收集")
            print("   - 查看生成的状态文件了解进度")
            print("   - 运行 python background_crawler.py 进入完整控制界面")
            print("\n⏳ 等待收集完成...")
            
            # 等待收集完成
            while controller.is_running:
                await asyncio.sleep(10)
                status = controller.get_status()
                if status.get("progress", 0) >= 100:
                    break
            
            print("\n🎉 收集完成!")
            controller.print_status()
            
            # 保存结果
            stats = controller.state_manager.get_statistics()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存URL列表
            urls_file = f"boss_urls_auto_{timestamp}.txt"
            with open(urls_file, 'w', encoding='utf-8') as f:
                for url in controller.state_manager.state["validated_urls"]:
                    f.write(f"{url}\n")
            
            print(f"📄 URL列表已保存: {urls_file}")
            print(f"✅ 共收集到 {stats.get('valid_urls', 0)} 个有效URL")
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
        controller.stop()
        print("✅ 已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("BOSS直聘后台爬虫启动脚本")
            print("用法:")
            print("  python start_background_crawler.py        # 快速启动")
            print("  python background_crawler.py              # 完整控制界面")
            return
    
    success = await quick_start()
    if not success:
        print("❌ 启动失败，请检查配置和网络连接")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
